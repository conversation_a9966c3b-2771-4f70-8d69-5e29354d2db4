/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/inventory/page";
exports.ids = ["app/inventory/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finventory%2Fpage&page=%2Finventory%2Fpage&appPaths=%2Finventory%2Fpage&pagePath=private-next-app-dir%2Finventory%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finventory%2Fpage&page=%2Finventory%2Fpage&appPaths=%2Finventory%2Fpage&pagePath=private-next-app-dir%2Finventory%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'inventory',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/inventory/page.tsx */ \"(rsc)/./src/app/inventory/page.tsx\")), \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/inventory/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/inventory/page\",\n        pathname: \"/inventory\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finventory%2Fpage&page=%2Finventory%2Fpage&appPaths=%2Finventory%2Fpage&pagePath=private-next-app-dir%2Finventory%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2RlbnRhbGNhcmUuaWQlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUQlM0ElNUNkZW50YWxjYXJlLmlkJTVDc3JjJTVDYXBwJTVDcHJvdmlkZXJzLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLz8xYWFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZGVudGFsY2FyZS5pZFxcXFxzcmNcXFxcYXBwXFxcXHByb3ZpZGVycy50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cinventory%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cinventory%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/inventory/page.tsx */ \"(ssr)/./src/app/inventory/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2RlbnRhbGNhcmUuaWQlNUNzcmMlNUNhcHAlNUNpbnZlbnRvcnklNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLz85YTVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZGVudGFsY2FyZS5pZFxcXFxzcmNcXFxcYXBwXFxcXGludmVudG9yeVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cinventory%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InventoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/mockData */ \"(ssr)/./src/data/mockData.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction InventoryPage() {\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [inventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_mockData__WEBPACK_IMPORTED_MODULE_3__.mockInventory);\n    const categories = [\n        \"all\",\n        ...Array.from(new Set(inventory.map((item)=>item.category)))\n    ];\n    const statuses = [\n        \"all\",\n        \"in-stock\",\n        \"low-stock\",\n        \"out-of-stock\",\n        \"expired\"\n    ];\n    const filteredInventory = inventory.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || item.category === selectedCategory;\n        const matchesStatus = statusFilter === \"all\" || item.status === statusFilter;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"id-ID\", {\n            style: \"currency\",\n            currency: \"IDR\",\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"id-ID\");\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"in-stock\":\n                return \"status-in-stock\";\n            case \"low-stock\":\n                return \"status-low-stock\";\n            case \"out-of-stock\":\n                return \"status-out-of-stock\";\n            case \"expired\":\n                return \"status-cancelled\";\n            default:\n                return \"status-in-stock\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"in-stock\":\n                return \"Tersedia\";\n            case \"low-stock\":\n                return \"Stok Menipis\";\n            case \"out-of-stock\":\n                return \"Habis\";\n            case \"expired\":\n                return \"Kadaluarsa\";\n            default:\n                return status;\n        }\n    };\n    const getStockLevel = (item)=>{\n        const percentage = item.currentStock / (item.minStock * 2) * 100;\n        return Math.min(percentage, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Manajemen Inventory\",\n                subtitle: \"Kelola stok alat dan bahan kedokteran gigi\"\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"p-6\",\n                children: [\n                    inventory.filter((item)=>item.status === \"low-stock\" || item.status === \"out-of-stock\").length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-yellow-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-800 font-medium\",\n                                    children: [\n                                        \"Perhatian: \",\n                                        inventory.filter((item)=>item.status === \"low-stock\" || item.status === \"out-of-stock\").length,\n                                        \" item memerlukan restocking\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Inventory Items\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-secondary flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Stock In\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-secondary flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Stock Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-primary flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Tambah Item\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Cari item...\",\n                                                className: \"input-field pl-10\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"input-field\",\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"Semua Kategori\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            categories.filter((cat)=>cat !== \"all\").map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"input-field\",\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"Semua Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"in-stock\",\n                                                children: \"Tersedia\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"low-stock\",\n                                                children: \"Stok Menipis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"out-of-stock\",\n                                                children: \"Habis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"expired\",\n                                                children: \"Kadaluarsa\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Item\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Stok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Harga\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Supplier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Kadaluarsa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Aksi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: filteredInventory.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: item.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 182,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-sm mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        item.currentStock,\n                                                                                        \" \",\n                                                                                        item.unit\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 189,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: [\n                                                                                        \"Min: \",\n                                                                                        item.minStock\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 190,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: `h-2 rounded-full ${item.currentStock <= item.minStock ? \"bg-red-500\" : item.currentStock <= item.minStock * 1.5 ? \"bg-yellow-500\" : \"bg-green-500\"}`,\n                                                                                style: {\n                                                                                    width: `${getStockLevel(item)}%`\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 193,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: formatCurrency(item.price)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: item.supplier\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: item.expiryDate ? formatDate(item.expiryDate) : \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `status-badge ${getStatusColor(item.status)}`,\n                                                                children: getStatusText(item.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-primary-600 hover:text-primary-900 mr-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-900\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            filteredInventory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"Tidak ada item inventory yang ditemukan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/inventory/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _lib_queryClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/queryClient */ \"(ssr)/./src/lib/queryClient.ts\");\n/* harmony import */ var _components_Layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/AppLayout */ \"(ssr)/./src/components/Layout/AppLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: _lib_queryClient__WEBPACK_IMPORTED_MODULE_3__.queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.TenantProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__.AppLayout, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTREO0FBQzVELHVFQUF1RTtBQUNqQjtBQUNJO0FBQ1Y7QUFDVTtBQUVuRCxTQUFTSyxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNOLHNFQUFtQkE7UUFBQ08sUUFBUUoseURBQVdBO2tCQUN0Qyw0RUFBQ0YsK0RBQVlBO3NCQUNYLDRFQUFDQyxtRUFBY0E7MEJBQ2IsNEVBQUNFLG1FQUFTQTs4QkFDUEU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeD85MzI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG4vLyBpbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBUZW5hbnRQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgeyBxdWVyeUNsaWVudCB9IGZyb20gJ0AvbGliL3F1ZXJ5Q2xpZW50JztcbmltcG9ydCB7IEFwcExheW91dCB9IGZyb20gJ0AvY29tcG9uZW50cy9MYXlvdXQvQXBwTGF5b3V0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICA8VGVuYW50UHJvdmlkZXI+XG4gICAgICAgICAgPEFwcExheW91dD5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L0FwcExheW91dD5cbiAgICAgICAgPC9UZW5hbnRQcm92aWRlcj5cbiAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgey8qIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAoXG4gICAgICAgIDxSZWFjdFF1ZXJ5RGV2dG9vbHMgaW5pdGlhbElzT3Blbj17ZmFsc2V9IC8+XG4gICAgICApfSAqL31cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUXVlcnlDbGllbnRQcm92aWRlciIsIkF1dGhQcm92aWRlciIsIlRlbmFudFByb3ZpZGVyIiwicXVlcnlDbGllbnQiLCJBcHBMYXlvdXQiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/Auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _utils_demoData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/demoData */ \"(ssr)/./src/utils/demoData.ts\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \n\n\n\n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signIn } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            await signIn(email, password);\n            router.push(\"/\");\n        } catch (error) {\n            setError(getErrorMessage(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getErrorMessage = (errorCode)=>{\n        switch(errorCode){\n            case \"auth/user-not-found\":\n                return \"Email tidak terdaftar\";\n            case \"auth/wrong-password\":\n                return \"Password salah\";\n            case \"auth/invalid-email\":\n                return \"Format email tidak valid\";\n            case \"auth/user-disabled\":\n                return \"Akun telah dinonaktifkan\";\n            case \"auth/too-many-requests\":\n                return \"Terlalu banyak percobaan login. Coba lagi nanti\";\n            default:\n                return \"Terjadi kesalahan saat login\";\n        }\n    };\n    // Demo accounts for testing\n    const demoAccounts = [\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Dokter\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Resepsionis\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Admin\"\n        }\n    ];\n    const handleDemoLogin = (demoEmail, demoPassword)=>{\n        setEmail(demoEmail);\n        setPassword(demoPassword);\n    };\n    const handleSetupDemo = async ()=>{\n        setSetupLoading(true);\n        setError(\"\");\n        try {\n            const success = await (0,_utils_demoData__WEBPACK_IMPORTED_MODULE_4__.setupDemoData)();\n            if (success) {\n                alert(\"Demo data berhasil dibuat! Silakan login dengan akun demo.\");\n            } else {\n                setError(\"Gagal membuat demo data\");\n            }\n        } catch (error) {\n            setError(\"Error: \" + error.message);\n        } finally{\n            setSetupLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-16 w-16 bg-primary-600 rounded-xl flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-2xl\",\n                                children: \"DC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Login ke DentalCare\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Sistem Manajemen Klinik Gigi\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-green-900 mb-2\",\n                            children: \"Setup Demo Data:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSetupDemo,\n                            disabled: setupLoading,\n                            className: \"w-full bg-green-600 hover:bg-green-700 text-white text-sm py-2 px-4 rounded disabled:opacity-50\",\n                            children: setupLoading ? \"Setting up...\" : \"Create Demo Data & Users\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-700 mt-1\",\n                            children: \"Klik ini untuk membuat data demo dan user untuk testing\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"Demo Accounts:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleDemoLogin(account.email, account.password),\n                                    className: \"block w-full text-left text-xs text-blue-700 hover:text-blue-900 hover:bg-blue-100 px-2 py-1 rounded\",\n                                    children: [\n                                        account.role,\n                                        \": \",\n                                        account.email\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"input-field\",\n                                            placeholder: \"Masukkan email Anda\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    className: \"input-field pr-10\",\n                                                    placeholder: \"Masukkan password Anda\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"remember-me\",\n                                            name: \"remember-me\",\n                                            type: \"checkbox\",\n                                            className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"remember-me\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: \"Ingat saya\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                        children: \"Lupa password?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full btn-primary flex justify-center items-center\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Logging in...\"\n                                ]\n                            }, void 0, true) : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"\\xa9 2024 DentalCare. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/Layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/LoginForm */ \"(ssr)/./src/components/Auth/LoginForm.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Layout/Sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\nfunction AppLayout({ children }) {\n    const { user, profile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Show loading spinner while checking auth\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this);\n    }\n    // Show login form if not authenticated\n    if (!user || !profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__.LoginForm, {}, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, this);\n    }\n    // Show main app layout for authenticated users\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _components_TenantManagement_TenantSwitcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TenantManagement/TenantSwitcher */ \"(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TenantManagement_TenantSwitcher__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-64\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Cari pasien, appointment...\",\n                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-80\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"relative p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: new Date().toLocaleDateString(\"id-ID\", {\n                                weekday: \"long\",\n                                year: \"numeric\",\n                                month: \"long\",\n                                day: \"numeric\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        name: \"Pasien\",\n        href: \"/patients\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Jadwal\",\n        href: \"/appointments\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Treatment\",\n        href: \"/treatments\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Inventory\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Laporan\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Pengaturan\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col w-64 bg-white border-r border-gray-200 h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"DC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"DentalCare\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-6 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: `sidebar-link ${isActive ? \"active\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"w-5 h-5 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                    children: \"Dr. Sarah Putri\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 truncate\",\n                                    children: \"Dokter Gigi\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx":
/*!************************************************************!*\
  !*** ./src/components/TenantManagement/TenantSwitcher.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TenantSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TenantSwitcher({ className = \"\" }) {\n    const { tenant, tenantId, loading, switchTenant, createTenant } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTenantName, setNewTenantName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // For demo purposes, we'll show available tenants\n    // In a real app, you might fetch this from a user's accessible tenants\n    const availableTenants = tenantId ? [\n        {\n            id: tenantId,\n            name: tenant?.name || \"Current Clinic\"\n        }\n    ] : [];\n    const handleSwitchTenant = async (newTenantId)=>{\n        if (newTenantId === tenantId) {\n            setIsOpen(false);\n            return;\n        }\n        try {\n            await switchTenant(newTenantId);\n            setIsOpen(false);\n        } catch (error) {\n            console.error(\"Failed to switch tenant:\", error);\n        // You might want to show a toast notification here\n        }\n    };\n    const handleCreateTenant = async ()=>{\n        if (!newTenantName.trim()) return;\n        try {\n            setIsCreating(true);\n            await createTenant({\n                name: newTenantName.trim(),\n                address: \"\",\n                phone: \"\",\n                email: profile?.email || \"\"\n            });\n            setNewTenantName(\"\");\n            setIsOpen(false);\n        } catch (error) {\n            console.error(\"Failed to create tenant:\", error);\n        // You might want to show a toast notification here\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center space-x-2 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-4 bg-gray-200 rounded animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-3 w-full px-3 py-2 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                children: tenant?.name || \"Select Clinic\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 truncate\",\n                                children: tenant?.address || \"No address set\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: `w-4 h-4 text-gray-400 transition-transform ${isOpen ? \"transform rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: [\n                        availableTenants.map((availableTenant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSwitchTenant(availableTenant.id),\n                                className: \"flex items-center w-full px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: availableTenant.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableTenant.id === tenantId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, availableTenant.id, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-100 mt-1 pt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"New clinic name\",\n                                                value: newTenantName,\n                                                onChange: (e)=>setNewTenantName(e.target.value),\n                                                className: \"flex-1 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                                                onKeyPress: (e)=>{\n                                                    if (e.key === \"Enter\") {\n                                                        handleCreateTenant();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCreateTenant,\n                                                disabled: !newTenantName.trim() || isCreating,\n                                                className: \"flex items-center justify-center w-8 h-8 text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Create a new clinic\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, async (user)=>{\n            setUser(user);\n            if (user) {\n                try {\n                    // Fetch user profile from Firestore\n                    const profileDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid));\n                    if (profileDoc.exists()) {\n                        const profileData = profileDoc.data();\n                        setProfile({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            ...profileData\n                        });\n                    } else {\n                        // Create default profile if doesn't exist\n                        const defaultProfile = {\n                            id: user.uid,\n                            name: user.displayName || \"User\",\n                            email: user.email || \"\",\n                            role: \"receptionist\",\n                            tenantId: `tenant_${user.uid}`,\n                            permissions: [\n                                \"read_patients\",\n                                \"manage_appointments\"\n                            ],\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), defaultProfile);\n                        setProfile(defaultProfile);\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user profile:\", error);\n                }\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, []);\n    const signIn = async (email, password)=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const signUp = async (email, password, profileData)=>{\n        try {\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n            const user = userCredential.user;\n            // Create user profile in Firestore\n            const newProfile = {\n                id: user.uid,\n                email: user.email || email,\n                ...profileData,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), newProfile);\n            setProfile(newProfile);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user || !profile) throw new Error(\"No user logged in\");\n        try {\n            const updatedProfile = {\n                ...profile,\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), updatedProfile, {\n                merge: true\n            });\n            setProfile(updatedProfile);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signIn,\n            signUp,\n            logout,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction TenantProvider({ children }) {\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tenantId, setTenantId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { user, profile } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadTenant = async ()=>{\n            if (!user || !profile?.tenantId) {\n                setTenant(null);\n                setTenantId(null);\n                setLoading(false);\n                return;\n            }\n            try {\n                const tenantDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", profile.tenantId, \"settings\", \"clinic\"));\n                if (tenantDoc.exists()) {\n                    const tenantData = tenantDoc.data();\n                    setTenant({\n                        ...tenantData,\n                        id: profile.tenantId\n                    });\n                    setTenantId(profile.tenantId);\n                } else {\n                    // Create default tenant settings if they don't exist\n                    const defaultTenant = {\n                        id: profile.tenantId,\n                        name: \"Klinik Gigi\",\n                        address: \"\",\n                        phone: \"\",\n                        email: profile.email,\n                        settings: {\n                            timezone: \"Asia/Jakarta\",\n                            currency: \"IDR\",\n                            dateFormat: \"DD/MM/YYYY\",\n                            businessHours: {\n                                start: \"08:00\",\n                                end: \"17:00\",\n                                days: [\n                                    \"monday\",\n                                    \"tuesday\",\n                                    \"wednesday\",\n                                    \"thursday\",\n                                    \"friday\",\n                                    \"saturday\"\n                                ]\n                            }\n                        },\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", profile.tenantId, \"settings\", \"clinic\"), defaultTenant);\n                    setTenant(defaultTenant);\n                    setTenantId(profile.tenantId);\n                }\n            } catch (error) {\n                console.error(\"Error loading tenant:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadTenant();\n    }, [\n        user,\n        profile\n    ]);\n    const switchTenant = async (newTenantId)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            setLoading(true);\n            // Update user's tenantId in global users collection\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.uid), {\n                tenantId: newTenantId,\n                updatedAt: new Date().toISOString()\n            }, {\n                merge: true\n            });\n            // Load new tenant data\n            const tenantDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", newTenantId, \"settings\", \"clinic\"));\n            if (tenantDoc.exists()) {\n                const tenantData = tenantDoc.data();\n                setTenant({\n                    ...tenantData,\n                    id: newTenantId\n                });\n                setTenantId(newTenantId);\n            }\n        } catch (error) {\n            console.error(\"Error switching tenant:\", error);\n            throw new Error(\"Failed to switch tenant\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateTenant = async (updates)=>{\n        if (!tenantId || !tenant) throw new Error(\"No tenant selected\");\n        try {\n            const updatedTenant = {\n                ...tenant,\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", tenantId, \"settings\", \"clinic\"), updatedTenant, {\n                merge: true\n            });\n            setTenant(updatedTenant);\n        } catch (error) {\n            console.error(\"Error updating tenant:\", error);\n            throw new Error(\"Failed to update tenant\");\n        }\n    };\n    const createTenant = async (tenantData)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            // Generate tenant ID (could be UUID or custom format)\n            const newTenantId = `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTenant = {\n                ...tenantData,\n                id: newTenantId,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // Create tenant settings\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", newTenantId, \"settings\", \"clinic\"), newTenant);\n            // Update user's tenantId\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.uid), {\n                tenantId: newTenantId,\n                updatedAt: new Date().toISOString()\n            }, {\n                merge: true\n            });\n            setTenant(newTenant);\n            setTenantId(newTenantId);\n            return newTenantId;\n        } catch (error) {\n            console.error(\"Error creating tenant:\", error);\n            throw new Error(\"Failed to create tenant\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            tenantId,\n            loading,\n            switchTenant,\n            updateTenant,\n            createTenant\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\contexts\\\\TenantContext.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\nconst useTenant = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (!context) {\n        throw new Error(\"useTenant must be used within TenantProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TenantContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/mockData.ts":
/*!******************************!*\
  !*** ./src/data/mockData.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockAppointments: () => (/* binding */ mockAppointments),\n/* harmony export */   mockDashboardStats: () => (/* binding */ mockDashboardStats),\n/* harmony export */   mockInventory: () => (/* binding */ mockInventory),\n/* harmony export */   mockPatients: () => (/* binding */ mockPatients),\n/* harmony export */   mockTreatments: () => (/* binding */ mockTreatments)\n/* harmony export */ });\nconst mockPatients = [\n    {\n        id: \"1\",\n        medicalRecordNumber: \"RM001\",\n        name: \"Budi Santoso\",\n        email: \"<EMAIL>\",\n        phone: \"081234567890\",\n        dateOfBirth: \"1985-03-15\",\n        address: \"Jl. Sudirman No. 123, Jakarta\",\n        nik: \"3171234567890001\",\n        gender: \"male\",\n        emergencyContact: {\n            name: \"Siti Santoso\",\n            phone: \"081234567891\",\n            relationship: \"Istri\"\n        },\n        medicalHistory: {\n            allergies: [\n                \"Penisilin\"\n            ],\n            medications: [\n                \"Paracetamol\"\n            ],\n            conditions: [\n                \"Hipertensi\"\n            ]\n        },\n        lastVisit: \"2024-01-15\",\n        totalVisits: 5,\n        status: \"active\"\n    },\n    {\n        id: \"2\",\n        medicalRecordNumber: \"RM002\",\n        name: \"Sari Dewi\",\n        email: \"<EMAIL>\",\n        phone: \"081234567892\",\n        dateOfBirth: \"1990-07-22\",\n        address: \"Jl. Thamrin No. 456, Jakarta\",\n        nik: \"3171234567890002\",\n        gender: \"female\",\n        emergencyContact: {\n            name: \"Ahmad Dewi\",\n            phone: \"081234567893\",\n            relationship: \"Suami\"\n        },\n        medicalHistory: {\n            allergies: [],\n            medications: [],\n            conditions: []\n        },\n        lastVisit: \"2024-01-10\",\n        totalVisits: 3,\n        status: \"active\"\n    },\n    {\n        id: \"3\",\n        medicalRecordNumber: \"RM003\",\n        name: \"Andi Wijaya\",\n        email: \"<EMAIL>\",\n        phone: \"081234567894\",\n        dateOfBirth: \"1988-11-08\",\n        address: \"Jl. Gatot Subroto No. 789, Jakarta\",\n        nik: \"3171234567890003\",\n        gender: \"male\",\n        emergencyContact: {\n            name: \"Maya Wijaya\",\n            phone: \"081234567895\",\n            relationship: \"Istri\"\n        },\n        medicalHistory: {\n            allergies: [\n                \"Sulfa\"\n            ],\n            medications: [],\n            conditions: [\n                \"Diabetes\"\n            ]\n        },\n        lastVisit: \"2024-01-08\",\n        totalVisits: 8,\n        status: \"active\"\n    }\n];\nconst mockAppointments = [\n    {\n        id: \"1\",\n        patientId: \"1\",\n        patientName: \"Budi Santoso\",\n        doctorId: \"doc1\",\n        doctorName: \"Dr. Sarah Putri\",\n        date: \"2024-01-20\",\n        time: \"09:00\",\n        duration: 60,\n        type: \"Konsultasi\",\n        status: \"scheduled\",\n        notes: \"Keluhan sakit gigi geraham kiri\",\n        treatmentPlan: [\n            \"Konsultasi\",\n            \"Scaling\"\n        ]\n    },\n    {\n        id: \"2\",\n        patientId: \"2\",\n        patientName: \"Sari Dewi\",\n        doctorId: \"doc1\",\n        doctorName: \"Dr. Sarah Putri\",\n        date: \"2024-01-20\",\n        time: \"10:30\",\n        duration: 90,\n        type: \"Scaling\",\n        status: \"confirmed\",\n        treatmentPlan: [\n            \"Scaling\",\n            \"Fluoride Treatment\"\n        ]\n    },\n    {\n        id: \"3\",\n        patientId: \"3\",\n        patientName: \"Andi Wijaya\",\n        doctorId: \"doc2\",\n        doctorName: \"Dr. Ahmad Rahman\",\n        date: \"2024-01-20\",\n        time: \"14:00\",\n        duration: 120,\n        type: \"Crown\",\n        status: \"in-progress\",\n        treatmentPlan: [\n            \"Crown Preparation\",\n            \"Temporary Crown\"\n        ]\n    }\n];\nconst mockTreatments = [\n    {\n        id: \"1\",\n        code: \"CONS001\",\n        name: \"Konsultasi\",\n        category: \"Konsultasi\",\n        price: 150000,\n        duration: 30,\n        description: \"Konsultasi dan pemeriksaan gigi\"\n    },\n    {\n        id: \"2\",\n        code: \"SCAL001\",\n        name: \"Scaling\",\n        category: \"Preventif\",\n        price: 300000,\n        duration: 60,\n        description: \"Pembersihan karang gigi\"\n    },\n    {\n        id: \"3\",\n        code: \"FILL001\",\n        name: \"Tambal Gigi\",\n        category: \"Restoratif\",\n        price: 250000,\n        duration: 45,\n        description: \"Penambalan gigi dengan komposit\"\n    },\n    {\n        id: \"4\",\n        code: \"CROW001\",\n        name: \"Crown\",\n        category: \"Prostetik\",\n        price: 2500000,\n        duration: 120,\n        description: \"Pemasangan mahkota gigi\"\n    }\n];\nconst mockInventory = [\n    {\n        id: \"1\",\n        name: \"Komposit Resin\",\n        category: \"Bahan Tambal\",\n        currentStock: 15,\n        minStock: 10,\n        unit: \"tube\",\n        price: 450000,\n        supplier: \"PT Dental Supply\",\n        expiryDate: \"2025-06-15\",\n        lastRestocked: \"2024-01-01\",\n        status: \"in-stock\"\n    },\n    {\n        id: \"2\",\n        name: \"Anestesi Lidocaine\",\n        category: \"Obat\",\n        currentStock: 5,\n        minStock: 20,\n        unit: \"vial\",\n        price: 25000,\n        supplier: \"PT Pharma Dental\",\n        expiryDate: \"2024-12-31\",\n        lastRestocked: \"2023-12-15\",\n        status: \"low-stock\"\n    },\n    {\n        id: \"3\",\n        name: \"Sarung Tangan Latex\",\n        category: \"Disposable\",\n        currentStock: 0,\n        minStock: 50,\n        unit: \"box\",\n        price: 85000,\n        supplier: \"PT Medical Supply\",\n        lastRestocked: \"2023-11-20\",\n        status: \"out-of-stock\"\n    }\n];\nconst mockDashboardStats = {\n    todayAppointments: 8,\n    todayRevenue: 2750000,\n    totalPatients: 156,\n    monthlyRevenue: 45000000,\n    pendingPayments: 3,\n    lowStockItems: 5\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/data/mockData.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk\",\n    authDomain: \"widigital-d6110.firebaseapp.com\",\n    projectId: \"widigital-d6110\",\n    storageBucket: \"widigital-d6110.firebasestorage.app\",\n    messagingSenderId: \"329879577024\",\n    appId: \"1:329879577024:web:0d8752f8175569f67d6825\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\n// Connect to emulators in development (disabled for now)\n// if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {\n//   try {\n//     // Only connect if not already connected\n//     if (!auth.config.emulator) {\n//       connectAuthEmulator(auth, 'http://localhost:9099');\n//     }\n//     // @ts-ignore\n//     if (!db._delegate._databaseId.projectId.includes('localhost')) {\n//       connectFirestoreEmulator(db, 'localhost', 8080);\n//     }\n//   } catch (error) {\n//     console.log('Emulators already connected or not available');\n//   }\n// }\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/queryClient.ts":
/*!********************************!*\
  !*** ./src/lib/queryClient.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryClient: () => (/* binding */ queryClient)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000,\n            retry: (failureCount, error)=>{\n                // Don't retry on auth errors\n                if (error?.code === \"permission-denied\" || error?.code === \"unauthenticated\") {\n                    return false;\n                }\n                return failureCount < 3;\n            },\n            refetchOnWindowFocus: false\n        },\n        mutations: {\n            retry: false\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3F1ZXJ5Q2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBRTdDLE1BQU1DLGNBQWMsSUFBSUQsOERBQVdBLENBQUM7SUFDekNFLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLFdBQVcsSUFBSSxLQUFLO1lBQ3BCQyxRQUFRLEtBQUssS0FBSztZQUNsQkMsT0FBTyxDQUFDQyxjQUFjQztnQkFDcEIsNkJBQTZCO2dCQUM3QixJQUFJQSxPQUFPQyxTQUFTLHVCQUF1QkQsT0FBT0MsU0FBUyxtQkFBbUI7b0JBQzVFLE9BQU87Z0JBQ1Q7Z0JBQ0EsT0FBT0YsZUFBZTtZQUN4QjtZQUNBRyxzQkFBc0I7UUFDeEI7UUFDQUMsV0FBVztZQUNUTCxPQUFPO1FBQ1Q7SUFDRjtBQUNGLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLy4vc3JjL2xpYi9xdWVyeUNsaWVudC50cz81MWIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFF1ZXJ5Q2xpZW50IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcblxuZXhwb3J0IGNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICBzdGFsZVRpbWU6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xuICAgICAgZ2NUaW1lOiAxMCAqIDYwICogMTAwMCwgLy8gMTAgbWludXRlcyAod2FzIGNhY2hlVGltZSlcbiAgICAgIHJldHJ5OiAoZmFpbHVyZUNvdW50LCBlcnJvcjogYW55KSA9PiB7XG4gICAgICAgIC8vIERvbid0IHJldHJ5IG9uIGF1dGggZXJyb3JzXG4gICAgICAgIGlmIChlcnJvcj8uY29kZSA9PT0gJ3Blcm1pc3Npb24tZGVuaWVkJyB8fCBlcnJvcj8uY29kZSA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhaWx1cmVDb3VudCA8IDM7XG4gICAgICB9LFxuICAgICAgcmVmZXRjaE9uV2luZG93Rm9jdXM6IGZhbHNlLFxuICAgIH0sXG4gICAgbXV0YXRpb25zOiB7XG4gICAgICByZXRyeTogZmFsc2UsXG4gICAgfSxcbiAgfSxcbn0pO1xuIl0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50IiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJnY1RpbWUiLCJyZXRyeSIsImZhaWx1cmVDb3VudCIsImVycm9yIiwiY29kZSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwibXV0YXRpb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/queryClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/demoData.ts":
/*!*******************************!*\
  !*** ./src/utils/demoData.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupDemoData: () => (/* binding */ setupDemoData)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n\n/**\n * Setup demo data for testing multi-tenant structure\n */ async function setupDemoData(tenantId) {\n    try {\n        console.log(\"Setting up demo data for multi-tenant structure...\");\n        // Use provided tenantId or generate a demo one\n        const demoTenantId = tenantId || `demo-tenant-${Date.now()}`;\n        // Create demo tenant settings\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", demoTenantId, \"settings\", \"clinic\"), {\n            id: demoTenantId,\n            name: \"Klinik Gigi DentalCare Demo\",\n            address: \"Jl. Sudirman No. 123, Jakarta Pusat\",\n            phone: \"(021) 1234-5678\",\n            email: \"<EMAIL>\",\n            settings: {\n                timezone: \"Asia/Jakarta\",\n                currency: \"IDR\",\n                dateFormat: \"DD/MM/YYYY\",\n                businessHours: {\n                    start: \"08:00\",\n                    end: \"17:00\",\n                    days: [\n                        \"monday\",\n                        \"tuesday\",\n                        \"wednesday\",\n                        \"thursday\",\n                        \"friday\",\n                        \"saturday\"\n                    ]\n                }\n            },\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        });\n        // Create demo users\n        const demoUsers = [\n            {\n                email: \"<EMAIL>\",\n                password: \"demo123\",\n                profile: {\n                    name: \"Dr. Sarah Putri\",\n                    role: \"doctor\",\n                    tenantId: demoTenantId,\n                    permissions: [\n                        \"read_patients\",\n                        \"write_patients\",\n                        \"manage_treatments\",\n                        \"read_appointments\",\n                        \"write_appointments\"\n                    ]\n                }\n            },\n            {\n                email: \"<EMAIL>\",\n                password: \"demo123\",\n                profile: {\n                    name: \"Siti Nurhaliza\",\n                    role: \"receptionist\",\n                    tenantId: demoTenantId,\n                    permissions: [\n                        \"read_patients\",\n                        \"write_patients\",\n                        \"manage_appointments\",\n                        \"manage_billing\"\n                    ]\n                }\n            },\n            {\n                email: \"<EMAIL>\",\n                password: \"demo123\",\n                profile: {\n                    name: \"Ahmad Rahman\",\n                    role: \"admin\",\n                    tenantId: demoTenantId,\n                    permissions: [\n                        \"full_access\"\n                    ]\n                }\n            }\n        ];\n        for (const user of demoUsers){\n            try {\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, user.email, user.password);\n                const userProfile = {\n                    id: userCredential.user.uid,\n                    email: user.email,\n                    ...user.profile,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                // Create global user document\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userCredential.user.uid), userProfile);\n                // Create tenant-specific user document\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", demoTenantId, \"users\", userCredential.user.uid), userProfile);\n                console.log(`Created user: ${user.email}`);\n            } catch (error) {\n                if (error.code === \"auth/email-already-in-use\") {\n                    console.log(`User ${user.email} already exists`);\n                } else {\n                    console.error(`Error creating user ${user.email}:`, error);\n                }\n            }\n        }\n        // Create demo patients\n        const demoPatients = [\n            {\n                medicalRecordNumber: \"RM2024001\",\n                name: \"Budi Santoso\",\n                email: \"<EMAIL>\",\n                phone: \"081234567890\",\n                dateOfBirth: \"1985-03-15\",\n                address: \"Jl. Sudirman No. 123, Jakarta\",\n                nik: \"3171234567890001\",\n                gender: \"male\",\n                emergencyContact: {\n                    name: \"Siti Santoso\",\n                    phone: \"081234567891\",\n                    relationship: \"Istri\"\n                },\n                medicalHistory: {\n                    allergies: [\n                        \"Penisilin\"\n                    ],\n                    medications: [\n                        \"Paracetamol\"\n                    ],\n                    conditions: [\n                        \"Hipertensi\"\n                    ]\n                },\n                clinicalImages: [],\n                dentalChart: initializeDentalChart(),\n                lastVisit: \"2024-01-15\",\n                totalVisits: 5,\n                status: \"active\",\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            },\n            {\n                medicalRecordNumber: \"RM2024002\",\n                name: \"Sari Dewi\",\n                email: \"<EMAIL>\",\n                phone: \"081234567892\",\n                dateOfBirth: \"1990-07-22\",\n                address: \"Jl. Thamrin No. 456, Jakarta\",\n                nik: \"3171234567890002\",\n                gender: \"female\",\n                emergencyContact: {\n                    name: \"Ahmad Dewi\",\n                    phone: \"081234567893\",\n                    relationship: \"Suami\"\n                },\n                medicalHistory: {\n                    allergies: [],\n                    medications: [],\n                    conditions: []\n                },\n                clinicalImages: [],\n                dentalChart: initializeDentalChart(),\n                lastVisit: \"2024-01-10\",\n                totalVisits: 3,\n                status: \"active\",\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            },\n            {\n                medicalRecordNumber: \"RM2024003\",\n                name: \"Andi Wijaya\",\n                email: \"<EMAIL>\",\n                phone: \"081234567894\",\n                dateOfBirth: \"1988-11-08\",\n                address: \"Jl. Gatot Subroto No. 789, Jakarta\",\n                nik: \"3171234567890003\",\n                gender: \"male\",\n                emergencyContact: {\n                    name: \"Maya Wijaya\",\n                    phone: \"081234567895\",\n                    relationship: \"Istri\"\n                },\n                medicalHistory: {\n                    allergies: [\n                        \"Sulfa\"\n                    ],\n                    medications: [],\n                    conditions: [\n                        \"Diabetes\"\n                    ]\n                },\n                clinicalImages: [],\n                dentalChart: initializeDentalChart(),\n                lastVisit: \"2024-01-08\",\n                totalVisits: 8,\n                status: \"active\",\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            }\n        ];\n        for (const patient of demoPatients){\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", demoTenantId, \"patients\"), patient);\n        }\n        // Create demo treatments\n        const demoTreatments = [\n            {\n                code: \"CONS001\",\n                name: \"Konsultasi\",\n                category: \"Konsultasi\",\n                price: 150000,\n                duration: 30,\n                description: \"Konsultasi dan pemeriksaan gigi\"\n            },\n            {\n                code: \"SCAL001\",\n                name: \"Scaling\",\n                category: \"Preventif\",\n                price: 300000,\n                duration: 60,\n                description: \"Pembersihan karang gigi\"\n            },\n            {\n                code: \"FILL001\",\n                name: \"Tambal Gigi\",\n                category: \"Restoratif\",\n                price: 250000,\n                duration: 45,\n                description: \"Penambalan gigi dengan komposit\"\n            },\n            {\n                code: \"CROW001\",\n                name: \"Crown\",\n                category: \"Prostetik\",\n                price: 2500000,\n                duration: 120,\n                description: \"Pemasangan mahkota gigi\"\n            }\n        ];\n        for (const treatment of demoTreatments){\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", demoTenantId, \"treatments\"), treatment);\n        }\n        // Create demo inventory\n        const demoInventory = [\n            {\n                name: \"Komposit Resin\",\n                category: \"Bahan Tambal\",\n                currentStock: 15,\n                minStock: 10,\n                unit: \"tube\",\n                price: 450000,\n                supplier: \"PT Dental Supply\",\n                expiryDate: \"2025-06-15\",\n                lastRestocked: \"2024-01-01\",\n                status: \"in-stock\"\n            },\n            {\n                name: \"Anestesi Lidocaine\",\n                category: \"Obat\",\n                currentStock: 5,\n                minStock: 20,\n                unit: \"vial\",\n                price: 25000,\n                supplier: \"PT Pharma Dental\",\n                expiryDate: \"2024-12-31\",\n                lastRestocked: \"2023-12-15\",\n                status: \"low-stock\"\n            },\n            {\n                name: \"Sarung Tangan Latex\",\n                category: \"Disposable\",\n                currentStock: 0,\n                minStock: 50,\n                unit: \"box\",\n                price: 85000,\n                supplier: \"PT Medical Supply\",\n                lastRestocked: \"2023-11-20\",\n                status: \"out-of-stock\"\n            }\n        ];\n        for (const item of demoInventory){\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", demoTenantId, \"inventory\"), item);\n        }\n        console.log(`Demo data setup completed for tenant: ${demoTenantId}`);\n        return {\n            success: true,\n            tenantId: demoTenantId\n        };\n    } catch (error) {\n        console.error(\"Error setting up demo data:\", error);\n        return {\n            success: false,\n            tenantId: null\n        };\n    }\n}\nfunction initializeDentalChart() {\n    const dentalChart = [];\n    for(let i = 1; i <= 32; i++){\n        dentalChart.push({\n            toothNumber: i,\n            condition: \"healthy\",\n            notes: \"\",\n            updatedAt: new Date().toISOString()\n        });\n    }\n    return dentalChart;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/demoData.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1a89328312eb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YWM2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFhODkzMjgzMTJlYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\inventory\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\nconst metadata = {\n    title: \"DentalCare - Sistem Manajemen Klinik Gigi\",\n    description: \"Aplikasi manajemen klinik gigi yang terintegrasi\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1QjtBQUVpQjtBQUVqQyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsaURBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tICcuL3Byb3ZpZGVycyc7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRGVudGFsQ2FyZSAtIFNpc3RlbSBNYW5hamVtZW4gS2xpbmlrIEdpZ2knLFxuICBkZXNjcmlwdGlvbjogJ0FwbGlrYXNpIG1hbmFqZW1lbiBrbGluaWsgZ2lnaSB5YW5nIHRlcmludGVncmFzaScsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImlkXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/@tanstack","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/@heroicons","vendor-chunks/lodash.camelcase","vendor-chunks/idb","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finventory%2Fpage&page=%2Finventory%2Fpage&appPaths=%2Finventory%2Fpage&pagePath=private-next-app-dir%2Finventory%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();