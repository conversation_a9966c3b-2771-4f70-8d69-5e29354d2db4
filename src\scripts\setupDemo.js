/**
 * Setup Demo Data Script
 * Run this to create demo data for testing the multi-tenant application
 */

const { setupDemoData } = require('../utils/demoData');

async function main() {
  try {
    console.log('🚀 Setting up demo data...');
    
    const result = await setupDemoData();
    
    if (result.success) {
      console.log('✅ Demo data created successfully!');
      console.log(`📋 Tenant ID: ${result.tenantId}`);
      console.log('');
      console.log('Demo users created:');
      console.log('- <EMAIL> (password: demo123)');
      console.log('- <EMAIL> (password: demo123)');
      console.log('- <EMAIL> (password: demo123)');
      console.log('');
      console.log('You can now login with any of these accounts!');
    } else {
      console.log('❌ Failed to create demo data');
    }
  } catch (error) {
    console.error('Error setting up demo data:', error);
  }
}

main();
